import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { SoftCard, SoftButton, SoftInput, ItemCard } from '../components';
import { theme } from '../theme';
import { mockItems } from '../data/mockData';

export const DemoScreen: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);

  const handleButtonPress = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  const handleItemPress = () => {
    console.log('Item pressed');
  };

  const handleLike = () => {
    console.log('Item liked');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.title}>Component Demo</Text>
        
        {/* Soft Card Demo */}
        <SoftCard style={styles.section}>
          <Text style={styles.sectionTitle}>Soft Card</Text>
          <Text style={styles.description}>
            This is a soft UI card with rounded corners and shadow effects.
          </Text>
        </SoftCard>

        {/* Button Demo */}
        <SoftCard style={styles.section}>
          <Text style={styles.sectionTitle}>Buttons</Text>
          <View style={styles.buttonRow}>
            <SoftButton
              title="Primary"
              onPress={handleButtonPress}
              loading={loading}
              style={styles.button}
            />
            <SoftButton
              title="Secondary"
              onPress={handleButtonPress}
              variant="secondary"
              style={styles.button}
            />
          </View>
        </SoftCard>

        {/* Input Demo */}
        <SoftCard style={styles.section}>
          <Text style={styles.sectionTitle}>Input Field</Text>
          <SoftInput
            label="Demo Input"
            value={inputValue}
            onChangeText={setInputValue}
            placeholder="Type something..."
          />
        </SoftCard>

        {/* Item Card Demo */}
        <SoftCard style={styles.section}>
          <Text style={styles.sectionTitle}>Item Card</Text>
          <View style={styles.itemContainer}>
            <ItemCard
              item={mockItems[0]}
              onPress={handleItemPress}
              onLike={handleLike}
            />
          </View>
        </SoftCard>

        {/* Color Palette Demo */}
        <SoftCard style={styles.section}>
          <Text style={styles.sectionTitle}>Color Palette</Text>
          <View style={styles.colorRow}>
            <View style={[styles.colorSwatch, { backgroundColor: theme.colors.primaryBackground }]} />
            <View style={[styles.colorSwatch, { backgroundColor: theme.colors.surfaceBackground }]} />
            <View style={[styles.colorSwatch, { backgroundColor: theme.colors.primaryAccent }]} />
            <View style={[styles.colorSwatch, { backgroundColor: theme.colors.primaryText }]} />
          </View>
        </SoftCard>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.primaryBackground,
  },
  
  content: {
    padding: theme.spacing['2xl'],
  },
  
  title: {
    fontSize: theme.typography.heading1.fontSize,
    fontWeight: theme.typography.heading1.fontWeight,
    color: theme.colors.primaryText,
    textAlign: 'center',
    marginBottom: theme.spacing['3xl'],
  },
  
  section: {
    marginBottom: theme.spacing.xl,
  },
  
  sectionTitle: {
    fontSize: theme.typography.heading2.fontSize,
    fontWeight: theme.typography.heading2.fontWeight,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.md,
  },
  
  description: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.secondaryText,
    lineHeight: theme.typography.lineHeight.relaxed * theme.typography.body.fontSize,
  },
  
  buttonRow: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  
  button: {
    flex: 1,
  },
  
  itemContainer: {
    alignItems: 'center',
  },
  
  colorRow: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  
  colorSwatch: {
    width: 50,
    height: 50,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.borderGray,
  },
});
