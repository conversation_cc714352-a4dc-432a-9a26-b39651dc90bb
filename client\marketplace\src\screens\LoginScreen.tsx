import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SoftCard, SoftButton, SoftInput } from '../components';
import { theme } from '../theme';
import { apiService, getErrorMessage } from '../services/api';
import { useAuth } from '../context/AuthContext';

interface LoginScreenProps {
  navigation: any;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      const response = await apiService.login(email, password);

      // Store the token and user data
      await StorageService.setAuthToken(response.data.token);
      await StorageService.setUserData(response.data.user);

      // Set token in API service for future requests
      apiService.setToken(response.data.token);

      setLoading(false);
      Alert.alert('Success', 'Login successful!', [
        { text: 'OK', onPress: () => navigation.navigate('MainTabs') }
      ]);
    } catch (error) {
      setLoading(false);
      const errorMessage = getErrorMessage(error);
      Alert.alert('Login Failed', errorMessage);
    }
  };

  const navigateToRegister = () => {
    navigation.navigate('Register');
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Welcome Back</Text>
          <Text style={styles.subtitle}>Sign in to your account</Text>
        </View>

        <SoftCard style={styles.formCard}>
          <SoftInput
            label="Email"
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email"
            keyboardType="email-address"
            autoCapitalize="none"
            error={errors.email}
          />

          <SoftInput
            label="Password"
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            secureTextEntry
            error={errors.password}
          />

          <SoftButton
            title="Sign In"
            onPress={handleLogin}
            loading={loading}
            style={styles.loginButton}
          />

          <View style={styles.registerSection}>
            <Text style={styles.registerText}>Don't have an account? </Text>
            <SoftButton
              title="Sign Up"
              onPress={navigateToRegister}
              variant="secondary"
              size="small"
            />
          </View>
        </SoftCard>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.primaryBackground,
  },

  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: theme.spacing['2xl'],
  },

  header: {
    alignItems: 'center',
    marginBottom: theme.spacing['4xl'],
  },

  title: {
    fontSize: theme.typography.heading1.fontSize,
    fontWeight: theme.typography.heading1.fontWeight,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.sm,
  },

  subtitle: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.secondaryText,
    textAlign: 'center',
  },

  formCard: {
    marginBottom: theme.spacing.xl,
  },

  loginButton: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },

  registerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  registerText: {
    fontSize: theme.typography.body.fontSize,
    color: theme.colors.secondaryText,
    marginRight: theme.spacing.sm,
  },
});
