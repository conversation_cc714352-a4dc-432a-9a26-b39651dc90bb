/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { AsyncStorageStatic } from "./types";
/**
 * `AsyncStorage` is a simple, unencrypted, asynchronous, persistent, key-value
 * storage system that is global to the app. It should be used instead of
 * LocalStorage.
 *
 * See https://react-native-async-storage.github.io/async-storage/docs/api
 */
declare const AsyncStorage: AsyncStorageStatic;
export default AsyncStorage;
//# sourceMappingURL=AsyncStorage.native.d.ts.map