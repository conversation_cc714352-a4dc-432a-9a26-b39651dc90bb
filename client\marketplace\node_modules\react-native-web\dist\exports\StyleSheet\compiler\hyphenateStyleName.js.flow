/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict
 */

const uppercasePattern = /[A-Z]/g;
const msPattern = /^ms-/;
const cache = {};
declare function toHyphenLower(match: any): any;
declare function hyphenateStyleName(name: string): string;
export default hyphenateStyleName;