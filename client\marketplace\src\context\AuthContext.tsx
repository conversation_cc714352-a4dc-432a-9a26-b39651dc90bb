import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { apiService, User } from '../services/api';
import { StorageService } from '../services/storage';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (token: string, userData: User) => Promise<void>;
  logout: () => Promise<void>;
  checkAuthStatus: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  const login = async (token: string, userData: User) => {
    try {
      // Store token and user data
      await StorageService.setAuthToken(token);
      await StorageService.setUserData(userData);
      
      // Set token in API service
      apiService.setToken(token);
      
      // Update state
      setUser(userData);
    } catch (error) {
      console.error('Error during login:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Try to call logout endpoint (optional)
      try {
        await apiService.logout();
      } catch (error) {
        // Ignore logout endpoint errors
        console.warn('Logout endpoint error:', error);
      }
      
      // Clear storage
      await StorageService.clearAll();
      
      // Clear API service token
      apiService.clearToken();
      
      // Update state
      setUser(null);
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      
      // Get stored token and user data
      const token = await StorageService.getAuthToken();
      const userData = await StorageService.getUserData();
      
      if (token && userData) {
        // Set token in API service
        apiService.setToken(token);
        
        try {
          // Verify token is still valid by calling /me endpoint
          const response = await apiService.getCurrentUser();
          setUser(response.data.user);
        } catch (error) {
          // Token is invalid, clear storage
          console.warn('Token validation failed:', error);
          await StorageService.clearAll();
          apiService.clearToken();
          setUser(null);
        }
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
