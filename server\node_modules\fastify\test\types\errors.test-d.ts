import { expectAssignable } from 'tsd'
import { errorCodes } from '../../fastify'
import { FastifyErrorConstructor } from '@fastify/error'

expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_VALIDATION)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_NOT_FOUND)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_OPTIONS_NOT_OBJ)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_QSP_NOT_FN)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCHEMA_CONTROLLER_BUCKET_OPT_NOT_FN)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCHEMA_ERROR_FORMATTER_NOT_FN)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_AJV_CUSTOM_OPTIONS_OPT_NOT_OBJ)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_AJV_CUSTOM_OPTIONS_OPT_NOT_ARR)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_VERSION_CONSTRAINT_NOT_STR)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_VALIDATION)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_ALREADY_PRESENT)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_INVALID_TYPE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_EMPTY_TYPE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_INVALID_HANDLER)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_INVALID_PARSE_TYPE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_BODY_TOO_LARGE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_INVALID_MEDIA_TYPE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_INVALID_CONTENT_LENGTH)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_EMPTY_JSON_BODY)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_CTP_INSTANCE_ALREADY_STARTED)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_DEC_ALREADY_PRESENT)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_DEC_DEPENDENCY_INVALID_TYPE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_DEC_MISSING_DEPENDENCY)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_DEC_AFTER_START)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_HOOK_INVALID_TYPE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_HOOK_INVALID_HANDLER)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_HOOK_INVALID_ASYNC_HANDLER)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_HOOK_NOT_SUPPORTED)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_MISSING_MIDDLEWARE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_HOOK_TIMEOUT)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_LOG_INVALID_DESTINATION)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_LOG_INVALID_LOGGER)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_REP_INVALID_PAYLOAD_TYPE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_REP_ALREADY_SENT)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_REP_SENT_VALUE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SEND_INSIDE_ONERR)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SEND_UNDEFINED_ERR)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_BAD_STATUS_CODE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_BAD_TRAILER_NAME)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_BAD_TRAILER_VALUE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_FAILED_ERROR_SERIALIZATION)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_MISSING_SERIALIZATION_FN)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_MISSING_CONTENTTYPE_SERIALIZATION_FN)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_REQ_INVALID_VALIDATION_INVOCATION)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCH_MISSING_ID)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCH_ALREADY_PRESENT)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCH_CONTENT_MISSING_SCHEMA)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCH_DUPLICATE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCH_VALIDATION_BUILD)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCH_SERIALIZATION_BUILD)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_SCH_RESPONSE_SCHEMA_NOT_NESTED_2XX)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_HTTP2_INVALID_VERSION)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_INIT_OPTS_INVALID)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_FORCE_CLOSE_CONNECTIONS_IDLE_NOT_AVAILABLE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_DUPLICATED_ROUTE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_BAD_URL)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ASYNC_CONSTRAINT)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_DEFAULT_ROUTE_INVALID_TYPE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_INVALID_URL)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_OPTIONS_NOT_OBJ)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_DUPLICATED_HANDLER)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_HANDLER_NOT_FN)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_MISSING_HANDLER)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_METHOD_INVALID)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_METHOD_NOT_SUPPORTED)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_BODY_VALIDATION_SCHEMA_NOT_SUPPORTED)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_BODY_LIMIT_OPTION_NOT_INT)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROUTE_REWRITE_NOT_STR)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_REOPENED_CLOSE_SERVER)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_REOPENED_SERVER)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_INSTANCE_ALREADY_LISTENING)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_PLUGIN_VERSION_MISMATCH)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_PLUGIN_NOT_PRESENT_IN_INSTANCE)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_PLUGIN_CALLBACK_NOT_FN)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_PLUGIN_NOT_VALID)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_ROOT_PLG_BOOTED)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_PARENT_PLUGIN_BOOTED)
expectAssignable<FastifyErrorConstructor>(errorCodes.FST_ERR_PLUGIN_TIMEOUT)
