{"version": 3, "names": ["checkValidArgs", "keyValuePairs", "callback", "Array", "isArray", "length", "Error", "checkValidInput", "input", "key", "value", "console", "warn", "convertError", "error", "out", "message", "convertErrors", "errs", "errors", "ensureArray", "map", "e"], "sourceRoot": "../../src", "sources": ["helpers.ts"], "mappings": ";;AAEA,OAAO,SAASA,cAAcA,CAC5BC,aAAiC,EACjCC,QAAiB,EACjB;EACA,IACE,CAACC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,IAC7BA,aAAa,CAACI,MAAM,KAAK,CAAC,IAC1B,CAACF,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,CAAC,CAAC,CAAC,EAChC;IACA,MAAM,IAAIK,KAAK,CACb,gFACF,CAAC;EACH;EAEA,IAAIJ,QAAQ,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAC9C,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;MAC3B,MAAM,IAAII,KAAK,CACb,6IACF,CAAC;IACH;IAEA,MAAM,IAAIA,KAAK,CACb,iEACF,CAAC;EACH;AACF;AAEA,OAAO,SAASC,eAAeA,CAAC,GAAGC,KAAgB,EAAE;EACnD,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAGF,KAAK;EAE1B,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;IAC3B;IACAE,OAAO,CAACC,IAAI,CACV,wBAAwB,OAAOH,GAAG,iHAAiHA,GAAG,IACxJ,CAAC;EACH;EAEA,IAAID,KAAK,CAACH,MAAM,GAAG,CAAC,IAAI,OAAOK,KAAK,KAAK,QAAQ,EAAE;IACjD,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,MAAM,IAAIJ,KAAK,CACb,gJAAgJI,KAAK,iBAAiBD,GAAG,IAC3K,CAAC;IACH,CAAC,MAAM;MACL;MACAE,OAAO,CAACC,IAAI,CACV,qCAAqCH,GAAG,4GAA4GC,KAAK,iBAAiBD,GAAG,IAC/K,CAAC;IACH;EACF;AACF;AAEA,OAAO,SAASI,YAAYA,CAACC,KAAiB,EAAgB;EAC5D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,MAAMC,GAAG,GAAG,IAAIT,KAAK,CAACQ,KAAK,CAACE,OAAO,CAAsB;EACzDD,GAAG,CAAC,KAAK,CAAC,GAAGD,KAAK,CAACL,GAAG;EACtB,OAAOM,GAAG;AACZ;AAEA,OAAO,SAASE,aAAaA,CAC3BC,IAAkB,EACkB;EACpC,MAAMC,MAAM,GAAGC,WAAW,CAACF,IAAI,CAAC;EAChC,OAAOC,MAAM,GAAGA,MAAM,CAACE,GAAG,CAAEC,CAAC,IAAKT,YAAY,CAACS,CAAC,CAAC,CAAC,GAAG,IAAI;AAC3D;AAEA,SAASF,WAAWA,CAACE,CAA2B,EAAsB;EACpE,IAAInB,KAAK,CAACC,OAAO,CAACkB,CAAC,CAAC,EAAE;IACpB,OAAOA,CAAC,CAACjB,MAAM,KAAK,CAAC,GAAG,IAAI,GAAGiB,CAAC;EAClC,CAAC,MAAM,IAAIA,CAAC,EAAE;IACZ,OAAO,CAACA,CAAC,CAAC;EACZ,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF", "ignoreList": []}