{"name": "avvio", "version": "8.4.0", "description": "Asynchronous bootstrapping of Node applications", "main": "boot.js", "type": "commonjs", "scripts": {"lint": "standard", "lint:fix": "standard --fix", "test": "npm run test:unit && npm run test:typescript", "test:unit": "tap", "test:typescript": "tsc --project ./test/types/tsconfig.json"}, "precommit": ["lint", "test"], "repository": {"type": "git", "url": "git+https://github.com/fastify/avvio.git"}, "keywords": ["async", "boot", "delayed", "open"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "url": "http://delved.org"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/avvio/issues"}, "homepage": "https://github.com/fastify/avvio#readme", "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.0", "express": "^4.17.1", "standard": "^17.0.0", "tap": "^16.0.0", "typescript": "^5.0.2"}, "dependencies": {"@fastify/error": "^3.3.0", "fastq": "^1.17.1"}}