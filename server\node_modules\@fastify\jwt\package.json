{"name": "@fastify/jwt", "version": "7.2.4", "description": "JWT utils for Fastify", "main": "jwt.js", "type": "commonjs", "types": "types/jwt.d.ts", "scripts": {"lint": "standard", "lint:fix": "standard --fix", "test": "npm run lint && npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "tap", "test:unit:report": "tap --cov --coverage-report=html", "test:unit:verbose": "tap -Rspec"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-jwt.git"}, "keywords": ["jwt", "json", "token", "jsonwebtoken", "fastify"], "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-jwt/issues"}, "homepage": "https://github.com/fastify/fastify-jwt#readme", "dependencies": {"@fastify/error": "^3.0.0", "@lukeed/ms": "^2.0.0", "fast-jwt": "^3.3.2", "fastify-plugin": "^4.0.0", "steed": "^1.1.3"}, "devDependencies": {"@fastify/cookie": "^9.0.4", "@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.1", "fastify": "^4.0.0-rc.2", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.29.0"}, "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}