// Simple test to verify server starts correctly
const fastify = require('fastify')({ logger: true });

async function start() {
  try {
    console.log('Testing server startup...');
    
    // Register CORS
    await fastify.register(require('@fastify/cors'), {
      origin: true,
      credentials: true
    });

    // Simple test route
    fastify.get('/test', async (request, reply) => {
      return { message: 'Server is working!' };
    });

    const port = 3000;
    const host = '0.0.0.0';
    
    await fastify.listen({ port, host });
    console.log(`Test server running on http://${host}:${port}`);
    console.log('Visit http://localhost:3000/test to verify');
    
  } catch (err) {
    console.error('Error starting server:', err);
    process.exit(1);
  }
}

start();
