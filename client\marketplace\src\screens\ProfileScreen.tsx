import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SoftCard, SoftButton } from '../components';
import { theme } from '../theme';

interface ProfileScreenProps {
  navigation: any;
}

export const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation }) => {
  const userStats = [
    { label: 'Items', value: '124', icon: 'grid-outline' },
    { label: 'Owners', value: '10.4K', icon: 'people-outline' },
    { label: 'Floor Price', value: '0.231', icon: 'diamond-outline' },
    { label: 'Volume', value: '4K', icon: 'trending-up-outline' },
  ];

  const handleLogout = () => {
    // In a real app, this would clear auth state and navigate to login
    console.log('Logout pressed');
  };

  const handleEditProfile = () => {
    console.log('Edit profile pressed');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        {/* Profile Header */}
        <SoftCard style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <Image
                source={{ uri: 'https://picsum.photos/120/120?random=user' }}
                style={styles.avatar}
              />
              <View style={styles.statusIndicator} />
            </View>
            
            <Text style={styles.userName}>Liam Carter</Text>
            <Text style={styles.userHandle}>Creator by Alimo Ko</Text>
            
            <SoftButton
              title="Edit Profile"
              onPress={handleEditProfile}
              variant="secondary"
              size="small"
              style={styles.editButton}
            />
          </View>
        </SoftCard>

        {/* Stats */}
        <SoftCard style={styles.statsCard}>
          <Text style={styles.sectionTitle}>Statistics</Text>
          <View style={styles.statsGrid}>
            {userStats.map((stat, index) => (
              <View key={index} style={styles.statItem}>
                <Ionicons
                  name={stat.icon as any}
                  size={20}
                  color={theme.colors.primaryAccent}
                  style={styles.statIcon}
                />
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
            ))}
          </View>
        </SoftCard>

        {/* Balance */}
        <SoftCard style={styles.balanceCard}>
          <Text style={styles.sectionTitle}>Your Balance</Text>
          <View style={styles.balanceContainer}>
            <Text style={styles.balanceAmount}>65.324559</Text>
            <Text style={styles.balanceCurrency}>ETH</Text>
          </View>
          <Text style={styles.balancePercentage}>+3.3%</Text>
        </SoftCard>

        {/* Menu Options */}
        <SoftCard style={styles.menuCard}>
          <Text style={styles.sectionTitle}>Settings</Text>
          
          <View style={styles.menuItem}>
            <Ionicons name="person-outline" size={20} color={theme.colors.secondaryText} />
            <Text style={styles.menuText}>Account Settings</Text>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.secondaryText} />
          </View>
          
          <View style={styles.menuItem}>
            <Ionicons name="notifications-outline" size={20} color={theme.colors.secondaryText} />
            <Text style={styles.menuText}>Notifications</Text>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.secondaryText} />
          </View>
          
          <View style={styles.menuItem}>
            <Ionicons name="help-circle-outline" size={20} color={theme.colors.secondaryText} />
            <Text style={styles.menuText}>Help & Support</Text>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.secondaryText} />
          </View>
        </SoftCard>

        {/* Logout Button */}
        <SoftButton
          title="Logout"
          onPress={handleLogout}
          variant="secondary"
          style={styles.logoutButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.primaryBackground,
  },
  
  content: {
    padding: theme.spacing['2xl'],
  },
  
  profileCard: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  
  profileHeader: {
    alignItems: 'center',
  },
  
  avatarContainer: {
    position: 'relative',
    marginBottom: theme.spacing.lg,
  },
  
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  
  statusIndicator: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: theme.colors.positiveGreen,
    borderWidth: 2,
    borderColor: theme.colors.surfaceBackground,
  },
  
  userName: {
    fontSize: theme.typography.heading2.fontSize,
    fontWeight: theme.typography.heading2.fontWeight,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.xs,
  },
  
  userHandle: {
    fontSize: theme.typography.caption.fontSize,
    color: theme.colors.secondaryText,
    marginBottom: theme.spacing.lg,
  },
  
  editButton: {
    paddingHorizontal: theme.spacing['2xl'],
  },
  
  statsCard: {
    marginBottom: theme.spacing.xl,
  },
  
  sectionTitle: {
    fontSize: theme.typography.heading3.fontSize,
    fontWeight: theme.typography.heading3.fontWeight,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.lg,
  },
  
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  
  statIcon: {
    marginBottom: theme.spacing.sm,
  },
  
  statValue: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semiBold,
    color: theme.colors.primaryText,
    marginBottom: theme.spacing.xs,
  },
  
  statLabel: {
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.secondaryText,
  },
  
  balanceCard: {
    marginBottom: theme.spacing.xl,
  },
  
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: theme.spacing.sm,
  },
  
  balanceAmount: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.primaryText,
    marginRight: theme.spacing.sm,
  },
  
  balanceCurrency: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.secondaryText,
  },
  
  balancePercentage: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.positiveGreen,
    fontWeight: theme.typography.fontWeight.medium,
  },
  
  menuCard: {
    marginBottom: theme.spacing.xl,
  },
  
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(209, 204, 199, 0.3)',
  },
  
  menuText: {
    flex: 1,
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.primaryText,
    marginLeft: theme.spacing.md,
  },
  
  logoutButton: {
    marginTop: theme.spacing.lg,
  },
});
