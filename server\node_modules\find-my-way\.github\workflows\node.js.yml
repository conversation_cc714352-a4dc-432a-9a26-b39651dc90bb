name: Node CI

on:
  push:
    branches:
      - main
      - next
  pull_request:

permissions:
  contents: read

jobs:
  test:
    name: Test
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version:
          - 14
          - 16
          - 18
          - 20
          - 21
          - 22
        os: 
          - ubuntu-latest
          - windows-latest
          - macOS-latest
        exclude:
          - os: windows-latest
            node-version: 14
          - os: macos-latest
            node-version: 14
          - os: macos-latest
            node-version: 16
          - os: windows-latest
            node-version: 22 

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}

    - name: Install
      run: |
        npm install

    - name: Lint
      run: |
        npm run test:lint

    - name: Test
      run: |
        npm test

    - name: Type Definitions
      run: |
        npm run test:typescript

  automerge:
    if: >
      github.event_name == 'pull_request' && github.event.pull_request.user.login == 'dependabot[bot]'
    needs: test
    runs-on: ubuntu-latest
    permissions:
      actions: write
      pull-requests: write
      contents: write
    steps:
    - uses: fastify/github-action-merge-dependabot@9e7bfb249c69139d7bdcd8d984f9665edd49020b # v3.10.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
