const fastify = require('fastify')({ logger: true });
const path = require('path');

// Register plugins
async function registerPlugins() {
  // CORS plugin
  await fastify.register(require('@fastify/cors'), {
    origin: true, // Allow all origins for development
    credentials: true
  });

  // JWT plugin
  await fastify.register(require('@fastify/jwt'), {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
  });
}

// Database setup
const Database = require('sqlite3').Database;
const dbPath = path.join(__dirname, 'database.sqlite');

let db;

function initializeDatabase() {
  return new Promise((resolve, reject) => {
    db = new Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
        reject(err);
      } else {
        console.log('Connected to SQLite database');
        resolve();
      }
    });
  });
}

// Authentication decorator
fastify.decorate('authenticate', async function(request, reply) {
  try {
    await request.jwtVerify();
  } catch (err) {
    reply.send(err);
  }
});

// Routes
async function registerRoutes() {
  // Health check
  fastify.get('/health', async (request, reply) => {
    return { status: 'ok', timestamp: new Date().toISOString() };
  });

  // Register auth routes
  await fastify.register(require('./routes/auth'), { prefix: '/api/auth' });
}

// Start server
async function start() {
  try {
    await registerPlugins();
    await initializeDatabase();
    await registerRoutes();
    
    const port = process.env.PORT || 3000;
    const host = process.env.HOST || '0.0.0.0';
    
    await fastify.listen({ port, host });
    console.log(`Server running on http://${host}:${port}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down gracefully...');
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      } else {
        console.log('Database connection closed');
      }
    });
  }
  await fastify.close();
  process.exit(0);
});

// Export db for use in routes
fastify.decorate('db', () => db);

start();
