{"version": 3, "names": ["_reactNative", "require", "shouldFallbackToLegacyNativeModule", "expoConstants", "NativeModules", "modulesConstants", "ExponentConstants", "isLegacySdkVersion", "appOwnership", "executionEnvironment", "includes"], "sourceRoot": "../../src", "sources": ["shouldFallbackToLegacyNativeModule.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,SAASC,kCAAkCA,CAAA,EAAY;EAC5D,MAAMC,aAAa,GACjBC,0BAAa,CAAC,sBAAsB,CAAC,EAAEC,gBAAgB,EAAEC,iBAAiB;EAE5E,IAAIH,aAAa,EAAE;IACjB;AACJ;AACA;AACA;AACA;IACI,MAAMI,kBAAkB,GACtBJ,aAAa,CAACK,YAAY,IAAI,CAACL,aAAa,CAACM,oBAAoB;;IAEnE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IACEF,kBAAkB,IAClB,CAAC,aAAa,EAAE,YAAY,CAAC,CAACG,QAAQ,CAACP,aAAa,CAACM,oBAAoB,CAAC,EAC1E;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}