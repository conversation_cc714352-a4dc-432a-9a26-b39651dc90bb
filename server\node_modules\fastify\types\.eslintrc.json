{
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "standard"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "env": { "node": true },
  "parserOptions": {
    "ecmaVersion": 6,
    "sourceType": "module",
    "project": "./types/tsconfig.eslint.json",
    "createDefaultProgram": true
  },
  "rules": {
    "no-console": "off",
    "@typescript-eslint/indent": ["error", 2],
    "semi": ["error", "never"],
    "import/export": "off" // this errors on multiple exports (overload interfaces)
  },
  "overrides": [
    {
      "files": ["*.d.ts","*.test-d.ts"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off"
      }
    },
    {
      "files": ["*.test-d.ts"],
      "rules": {
        "no-unused-vars": "off",
        "n/handle-callback-err": "off",
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/explicit-function-return-type": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "@typescript-eslint/no-misused-promises": ["error", {
          "checksVoidReturn": false
        }]
      },
      "globals": {
        "NodeJS": "readonly"
      }
    }
  ]
}
