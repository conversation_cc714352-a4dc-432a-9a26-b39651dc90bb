{"version": 3, "names": ["checkValidArgs", "checkValidInput", "convertError", "convertErrors", "RCTAsyncStorage", "Error", "AsyncStorage", "_getRequests", "_get<PERSON>eys", "_immediate", "getItem", "key", "callback", "Promise", "resolve", "reject", "multiGet", "errors", "result", "value", "errs", "setItem", "multiSet", "removeItem", "multiRemove", "mergeItem", "multiMerge", "clear", "error", "err", "getAllKeys", "keys", "flushGetRequests", "getRequests", "get<PERSON><PERSON><PERSON>", "map", "for<PERSON>ach", "reqL<PERSON>th", "length", "errorList", "i", "request", "requestResult", "setImmediate", "getRequest", "keyIndex", "promiseResult", "push", "indexOf", "keyValuePairs"], "sourceRoot": "../../src", "sources": ["AsyncStorage.native.ts"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SACEA,cAAc,EACdC,eAAe,EACfC,YAAY,EACZC,aAAa,QACR,WAAW;AAClB,OAAOC,eAAe,MAAM,mBAAmB;AAQ/C,IAAI,CAACA,eAAe,EAAE;EACpB,MAAM,IAAIC,KAAK,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,CAAC,MAA0B;EAC9C,IAAIC,YAA4B,GAAG,EAAE;EACrC,IAAIC,QAAkB,GAAG,EAAE;EAC3B,IAAIC,UAAkD,GAAG,IAAI;EAE7D,OAAO;IACL;AACJ;AACA;AACA;AACA;IACIC,OAAO,EAAEA,CAACC,GAAG,EAAEC,QAAQ,KAAK;MAC1B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCd,eAAe,CAACU,GAAG,CAAC;QACpBP,eAAe,CAACY,QAAQ,CACtB,CAACL,GAAG,CAAC,EACL,CAACM,MAAoB,EAAEC,MAAmB,KAAK;UAC7C;UACA,MAAMC,KAAK,GAAGD,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;UACpD,MAAME,IAAI,GAAGjB,aAAa,CAACc,MAAM,CAAC;UAClCL,QAAQ,GAAGQ,IAAI,GAAG,CAAC,CAAC,EAAED,KAAK,CAAC;UAC5B,IAAIC,IAAI,EAAE;YACRL,MAAM,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACLN,OAAO,CAACK,KAAK,CAAC;UAChB;QACF,CACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIE,OAAO,EAAEA,CAACV,GAAG,EAAEQ,KAAK,EAAEP,QAAQ,KAAK;MACjC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCd,eAAe,CAACU,GAAG,EAAEQ,KAAK,CAAC;QAC3Bf,eAAe,CAACkB,QAAQ,CAAC,CAAC,CAACX,GAAG,EAAEQ,KAAK,CAAC,CAAC,EAAGF,MAAoB,IAAK;UACjE,MAAMG,IAAI,GAAGjB,aAAa,CAACc,MAAM,CAAC;UAClCL,QAAQ,GAAGQ,IAAI,GAAG,CAAC,CAAC,CAAC;UACrB,IAAIA,IAAI,EAAE;YACRL,MAAM,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACLN,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIS,UAAU,EAAEA,CAACZ,GAAG,EAAEC,QAAQ,KAAK;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCd,eAAe,CAACU,GAAG,CAAC;QACpBP,eAAe,CAACoB,WAAW,CAAC,CAACb,GAAG,CAAC,EAAGM,MAAoB,IAAK;UAC3D,MAAMG,IAAI,GAAGjB,aAAa,CAACc,MAAM,CAAC;UAClCL,QAAQ,GAAGQ,IAAI,GAAG,CAAC,CAAC,CAAC;UACrB,IAAIA,IAAI,EAAE;YACRL,MAAM,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACLN,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIW,SAAS,EAAEA,CAACd,GAAG,EAAEQ,KAAK,EAAEP,QAAQ,KAAK;MACnC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCd,eAAe,CAACU,GAAG,EAAEQ,KAAK,CAAC;QAC3Bf,eAAe,CAACsB,UAAU,CAAC,CAAC,CAACf,GAAG,EAAEQ,KAAK,CAAC,CAAC,EAAGF,MAAoB,IAAK;UACnE,MAAMG,IAAI,GAAGjB,aAAa,CAACc,MAAM,CAAC;UAClCL,QAAQ,GAAGQ,IAAI,GAAG,CAAC,CAAC,CAAC;UACrB,IAAIA,IAAI,EAAE;YACRL,MAAM,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACLN,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACIa,KAAK,EAAGf,QAAQ,IAAK;MACnB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCX,eAAe,CAACuB,KAAK,CAAEC,KAAiB,IAAK;UAC3C,MAAMC,GAAG,GAAG3B,YAAY,CAAC0B,KAAK,CAAC;UAC/BhB,QAAQ,GAAGiB,GAAG,CAAC;UACf,IAAIA,GAAG,EAAE;YACPd,MAAM,CAACc,GAAG,CAAC;UACb,CAAC,MAAM;YACLf,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIgB,UAAU,EAAGlB,QAAQ,IAAK;MACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCX,eAAe,CAAC0B,UAAU,CAAC,CAACF,KAAiB,EAAEG,IAAe,KAAK;UACjE,MAAMF,GAAG,GAAG3B,YAAY,CAAC0B,KAAK,CAAC;UAC/BhB,QAAQ,GAAGiB,GAAG,EAAEE,IAAI,CAAC;UACrB,IAAIA,IAAI,EAAE;YACRjB,OAAO,CAACiB,IAAI,CAAC;UACf,CAAC,MAAM;YACLhB,MAAM,CAACc,GAAG,CAAC;UACb;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEI;AACJ;AACA;AACA;AACA;IACIG,gBAAgB,EAAEA,CAAA,KAAM;MACtB,MAAMC,WAAW,GAAG1B,YAAY;MAChC,MAAM2B,OAAO,GAAG1B,QAAQ;MAExBD,YAAY,GAAG,EAAE;MACjBC,QAAQ,GAAG,EAAE;MAEbJ,eAAe,CAACY,QAAQ,CACtBkB,OAAO,EACP,CAACjB,MAAoB,EAAEC,MAAmB,KAAK;QAC7C;QACA;QACA;QACA;QACA;QACA;QACA,MAAMiB,GAA2B,GAAG,CAAC,CAAC;QACtCjB,MAAM,EAAEkB,OAAO,CAAC,CAAC,CAACzB,GAAG,EAAEQ,KAAK,CAAC,KAAK;UAChCgB,GAAG,CAACxB,GAAG,CAAC,GAAGQ,KAAK;UAChB,OAAOA,KAAK;QACd,CAAC,CAAC;QACF,MAAMkB,SAAS,GAAGJ,WAAW,CAACK,MAAM;;QAEpC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACU,MAAMC,SAAS,GAAGpC,aAAa,CAACc,MAAM,CAAC;QACvC,MAAMW,KAAK,GAAGW,SAAS,EAAED,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;QAErD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,EAAEG,CAAC,EAAE,EAAE;UAClC,MAAMC,OAAO,GAAGR,WAAW,CAACO,CAAC,CAAC;UAC9B,IAAIZ,KAAK,EAAE;YACTa,OAAO,CAAC7B,QAAQ,GAAG2B,SAAS,CAAC;YAC7BE,OAAO,CAAC1B,MAAM,GAAGa,KAAK,CAAC;YACvB;UACF;UACA,MAAMc,aAAa,GAAGD,OAAO,CAACV,IAAI,CAACI,GAAG,CAAgBxB,GAAG,IAAK,CAC5DA,GAAG,EACHwB,GAAG,CAACxB,GAAG,CAAC,CACT,CAAC;UACF8B,OAAO,CAAC7B,QAAQ,GAAG,IAAI,EAAE8B,aAAa,CAAC;UACvCD,OAAO,CAAC3B,OAAO,GAAG4B,aAAa,CAAC;QAClC;MACF,CACF,CAAC;IACH,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACI1B,QAAQ,EAAEA,CAACe,IAAI,EAAEnB,QAAQ,KAAK;MAC5B,IAAI,CAACH,UAAU,EAAE;QACfA,UAAU,GAAGkC,YAAY,CAAC,MAAM;UAC9BlC,UAAU,GAAG,IAAI;UACjBH,YAAY,CAAC0B,gBAAgB,CAAC,CAAC;QACjC,CAAC,CAAC;MACJ;MAEA,MAAMY,UAAwB,GAAG;QAC/Bb,IAAI,EAAEA,IAAI;QACVnB,QAAQ,EAAEA,QAAQ;QAClB;QACAiC,QAAQ,EAAErC,QAAQ,CAAC8B;MACrB,CAAC;MAED,MAAMQ,aAAa,GAAG,IAAIjC,OAAO,CAC/B,CAACC,OAAO,EAAEC,MAAM,KAAK;QACnB6B,UAAU,CAAC9B,OAAO,GAAGA,OAAO;QAC5B8B,UAAU,CAAC7B,MAAM,GAAGA,MAAM;MAC5B,CACF,CAAC;MAEDR,YAAY,CAACwC,IAAI,CAACH,UAAU,CAAC;MAC7B;MACAb,IAAI,CAACK,OAAO,CAAEzB,GAAG,IAAK;QACpB,IAAIH,QAAQ,CAACwC,OAAO,CAACrC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UAChCH,QAAQ,CAACuC,IAAI,CAACpC,GAAG,CAAC;QACpB;MACF,CAAC,CAAC;MAEF,OAAOmC,aAAa;IACtB,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIxB,QAAQ,EAAEA,CAAC2B,aAAa,EAAErC,QAAQ,KAAK;MACrCZ,cAAc,CAACiD,aAAa,EAAErC,QAAQ,CAAC;MACvC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCkC,aAAa,CAACb,OAAO,CAAC,CAAC,CAACzB,GAAG,EAAEQ,KAAK,CAAC,KAAK;UACtClB,eAAe,CAACU,GAAG,EAAEQ,KAAK,CAAC;QAC7B,CAAC,CAAC;QAEFf,eAAe,CAACkB,QAAQ,CAAC2B,aAAa,EAAGhC,MAAoB,IAAK;UAChE,MAAMW,KAAK,GAAGzB,aAAa,CAACc,MAAM,CAAC;UACnCL,QAAQ,GAAGgB,KAAK,CAAC;UACjB,IAAIA,KAAK,EAAE;YACTb,MAAM,CAACa,KAAK,CAAC;UACf,CAAC,MAAM;YACLd,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIU,WAAW,EAAEA,CAACO,IAAI,EAAEnB,QAAQ,KAAK;MAC/B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCgB,IAAI,CAACK,OAAO,CAAEzB,GAAG,IAAKV,eAAe,CAACU,GAAG,CAAC,CAAC;QAE3CP,eAAe,CAACoB,WAAW,CAACO,IAAI,EAAGd,MAAoB,IAAK;UAC1D,MAAMW,KAAK,GAAGzB,aAAa,CAACc,MAAM,CAAC;UACnCL,QAAQ,GAAGgB,KAAK,CAAC;UACjB,IAAIA,KAAK,EAAE;YACTb,MAAM,CAACa,KAAK,CAAC;UACf,CAAC,MAAM;YACLd,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIY,UAAU,EAAEA,CAACuB,aAAa,EAAErC,QAAQ,KAAK;MACvC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtCX,eAAe,CAACsB,UAAU,CAACuB,aAAa,EAAGhC,MAAoB,IAAK;UAClE,MAAMW,KAAK,GAAGzB,aAAa,CAACc,MAAM,CAAC;UACnCL,QAAQ,GAAGgB,KAAK,CAAC;UACjB,IAAIA,KAAK,EAAE;YACTb,MAAM,CAACa,KAAK,CAAC;UACf,CAAC,MAAM;YACLd,OAAO,CAAC,CAAC;UACX;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;AACH,CAAC,EAAE,CAAC;AAEJ,eAAeR,YAAY", "ignoreList": []}