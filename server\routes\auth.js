const bcrypt = require('bcryptjs');

async function authRoutes(fastify, options) {
  const db = fastify.db();

  // Helper function to run database queries as promises
  function runQuery(query, params = []) {
    return new Promise((resolve, reject) => {
      db.run(query, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  function getQuery(query, params = []) {
    return new Promise((resolve, reject) => {
      db.get(query, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // Validation schemas
  const loginSchema = {
    body: {
      type: 'object',
      required: ['email', 'password'],
      properties: {
        email: { type: 'string', format: 'email' },
        password: { type: 'string', minLength: 6 }
      }
    }
  };

  const registerSchema = {
    body: {
      type: 'object',
      required: ['email', 'password', 'name'],
      properties: {
        email: { type: 'string', format: 'email' },
        password: { type: 'string', minLength: 6 },
        name: { type: 'string', minLength: 1 }
      }
    }
  };

  // Register endpoint
  fastify.post('/register', { schema: registerSchema }, async (request, reply) => {
    try {
      const { email, password, name } = request.body;

      // Check if user already exists
      const existingUser = await getQuery('SELECT id FROM users WHERE email = ?', [email]);
      if (existingUser) {
        return reply.code(400).send({
          error: 'User already exists',
          message: 'An account with this email already exists'
        });
      }

      // Hash password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Insert new user
      const result = await runQuery(
        'INSERT INTO users (email, password, name) VALUES (?, ?, ?)',
        [email, hashedPassword, name]
      );

      // Generate JWT token
      const token = fastify.jwt.sign(
        { userId: result.id, email },
        { expiresIn: '7d' }
      );

      // Return success response
      reply.code(201).send({
        success: true,
        message: 'User registered successfully',
        data: {
          user: {
            id: result.id,
            email,
            name
          },
          token
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.code(500).send({
        error: 'Internal server error',
        message: 'Failed to register user'
      });
    }
  });

  // Login endpoint
  fastify.post('/login', { schema: loginSchema }, async (request, reply) => {
    try {
      const { email, password } = request.body;

      // Find user by email
      const user = await getQuery('SELECT * FROM users WHERE email = ?', [email]);
      if (!user) {
        return reply.code(401).send({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return reply.code(401).send({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        });
      }

      // Generate JWT token
      const token = fastify.jwt.sign(
        { userId: user.id, email: user.email },
        { expiresIn: '7d' }
      );

      // Return success response
      reply.send({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name
          },
          token
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.code(500).send({
        error: 'Internal server error',
        message: 'Failed to login'
      });
    }
  });

  // Get current user (protected route)
  fastify.get('/me', { preHandler: [fastify.authenticate] }, async (request, reply) => {
    try {
      const userId = request.user.userId;
      
      const user = await getQuery('SELECT id, email, name, created_at FROM users WHERE id = ?', [userId]);
      if (!user) {
        return reply.code(404).send({
          error: 'User not found',
          message: 'User account not found'
        });
      }

      reply.send({
        success: true,
        data: {
          user
        }
      });

    } catch (error) {
      fastify.log.error(error);
      reply.code(500).send({
        error: 'Internal server error',
        message: 'Failed to get user information'
      });
    }
  });

  // Logout endpoint (optional - mainly for token invalidation if using sessions)
  fastify.post('/logout', { preHandler: [fastify.authenticate] }, async (request, reply) => {
    try {
      // In a JWT-based system, logout is typically handled client-side by removing the token
      // But we can provide this endpoint for consistency
      reply.send({
        success: true,
        message: 'Logged out successfully'
      });
    } catch (error) {
      fastify.log.error(error);
      reply.code(500).send({
        error: 'Internal server error',
        message: 'Failed to logout'
      });
    }
  });
}

module.exports = authRoutes;
